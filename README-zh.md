# FreeHubGames 🎮

**The Ultimate Free Online Gaming Platform**

FreeHubGames is a modern, full-featured online gaming platform built with Next.js 15, offering 500+ free browser-based games across multiple categories. Experience seamless gaming with our responsive design, multi-language support, and user-friendly interface.

[![Live Demo](https://img.shields.io/badge/Live-Demo-brightgreen)](https://freehubgames.com)
[![Next.js](https://img.shields.io/badge/Next.js-15.0.3-black)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue)](https://www.typescriptlang.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🌟 Features

### 🎮 Gaming Experience
- **500+ Free Games**: Extensive collection across 8 categories (Action, Adventure, Racing, Shooting, Puzzle, Strategy, Sports, Simulation)
- **Instant Play**: No downloads required - play directly in your browser
- **Full-Screen Mode**: Immersive gaming experience with fullscreen support
- **Game Discovery**: Advanced categorization and search functionality
- **Mobile Optimized**: Responsive design for seamless mobile gaming

### 🌐 Platform Features
- **Multi-Language Support**: Full internationalization (English/Chinese) with next-intl
- **User Authentication**: Secure OAuth login with Google and GitHub via NextAuth.js
- **Premium Features**: Stripe-powered subscription system for enhanced gaming experience
- **SEO Optimized**: Automatic sitemap generation and meta tag optimization
- **Modern UI**: Beautiful interface built with Radix UI and Tailwind CSS
- **Performance**: Lightning-fast loading with Next.js 15 and Turbopack

## 🛠️ Technology Stack

### Frontend
- **Framework**: Next.js 15.0.3 with App Router
- **Language**: TypeScript 5.x for type safety
- **UI Library**: React 18.2.0
- **Styling**: Tailwind CSS 3.4.1 with custom animations
- **Components**: Radix UI primitives (Dialog, Dropdown, Accordion)
- **Icons**: Lucide React icon library
- **Animations**: Framer Motion for smooth interactions

### Backend & Database
- **API**: Next.js API Routes with TypeScript
- **Database**: PostgreSQL with Prisma ORM 6.1.0
- **Authentication**: NextAuth.js v4 with OAuth providers
- **Payments**: Stripe 17.5.0 integration
- **File Storage**: Optimized image handling with Next.js Image

### Development & Deployment
- **Build Tool**: Turbopack for ultra-fast development
- **Internationalization**: next-intl 3.26.3 for multi-language support
- **SEO**: next-sitemap for automatic sitemap generation
- **Deployment**: Vercel-optimized with automatic CI/CD
- **Package Manager**: pnpm for efficient dependency management

## 📋 Prerequisites

- **Node.js**: 18.17 or higher
- **Package Manager**: pnpm 8.0+ (recommended) or npm/yarn
- **Database**: PostgreSQL 12+ (recommended)
- **Git**: For version control

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/wenhaofree/game-grove-web.git
cd game-grove-web
```

### 2. Install Dependencies

```bash
pnpm install
# or
npm install
```

### 3. Environment Configuration

Create your environment file:

```bash
cp .env.example .env.local
```

Configure the following environment variables:

| Variable | Description | Example |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `postgresql://user:pass@localhost:5432/freehubgames` |
| `NEXTAUTH_SECRET` | NextAuth.js secret key | `your-super-secret-key` |
| `NEXTAUTH_URL` | Application URL | `http://localhost:3000` |
| `AUTH_GOOGLE_ID` | Google OAuth Client ID | `your-google-client-id` |
| `AUTH_GOOGLE_SECRET` | Google OAuth Client Secret | `your-google-client-secret` |
| `AUTH_GITHUB_ID` | GitHub OAuth App ID | `your-github-app-id` |
| `AUTH_GITHUB_SECRET` | GitHub OAuth App Secret | `your-github-app-secret` |
| `STRIPE_PUBLIC_KEY` | Stripe publishable key | `pk_test_...` |
| `STRIPE_PRIVATE_KEY` | Stripe secret key | `sk_test_...` |
| `STRIPE_WEBHOOK_SECRET` | Stripe webhook endpoint secret | `whsec_...` |

### 4. Database Setup

Initialize your PostgreSQL database:

```bash
# Generate Prisma client
pnpm db:generate

# Push database schema
pnpm db:push

# (Optional) Open Prisma Studio to manage data
pnpm db:studio
```

### 5. Start Development Server

```bash
pnpm dev
```

🎉 **Success!** Open [http://localhost:3000](http://localhost:3000) to see FreeHubGames running locally.

## 🎮 Game Categories

Our platform features games across 8 main categories:

- **🎯 Action**: Fast-paced games including shooters, fighting, and arcade-style games
- **🗺️ Adventure**: Story-driven games with exploration and puzzle-solving elements
- **🏎️ Racing**: High-speed racing games and driving simulators
- **🔫 Shooting**: First-person and third-person shooter games
- **🧩 Puzzle**: Brain-teasing games that challenge logic and problem-solving skills
- **⚔️ Strategy**: Tactical games requiring planning and strategic thinking
- **⚽ Sports**: Virtual sports games including football, basketball, and more
- **🎯 Simulation**: Life simulation and management games

## 📜 Available Scripts

### Development Commands
```bash
# Start development server with Turbopack
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start

# Run ESLint code analysis
pnpm lint
```

### Database Commands
```bash
# Generate Prisma client
pnpm db:generate

# Push schema changes to database
pnpm db:push

# Pull schema from existing database
pnpm db:pull

# Open Prisma Studio (database GUI)
pnpm db:studio

# Sync database schema (pull + push + generate)
pnpm db:sync
```

## 🚀 Deployment

### Deploy to Vercel (Recommended)

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fwenhaofree%2Fgame-grove-web&env=DATABASE_URL,NEXTAUTH_SECRET,NEXTAUTH_URL,AUTH_GOOGLE_ID,AUTH_GOOGLE_SECRET,AUTH_GITHUB_ID,AUTH_GITHUB_SECRET,STRIPE_PUBLIC_KEY,STRIPE_PRIVATE_KEY,STRIPE_WEBHOOK_SECRET&project-name=freehubgames&repository-name=freehubgames)

**Step-by-step deployment:**

1. **Fork the repository** to your GitHub account
2. **Create a new project** on [Vercel](https://vercel.com)
3. **Import your forked repository**
4. **Configure environment variables** (see Environment Configuration section)
5. **Deploy** - Vercel will automatically build and deploy your application

### Alternative Deployment Options

- **Netlify**: Compatible with static export
- **Railway**: Full-stack deployment with database
- **DigitalOcean App Platform**: Container-based deployment
- **Self-hosted**: Docker support available

## 📁 Project Structure

```
game-grove-web/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── [locale]/          # Internationalized routes
│   │   │   ├── category/      # Game category pages
│   │   │   ├── game/          # Individual game pages
│   │   │   └── page.tsx       # Home page
│   │   ├── api/               # API routes
│   │   │   ├── auth/          # Authentication endpoints
│   │   │   ├── orders/        # Payment/order management
│   │   │   └── stripe/        # Stripe webhook handlers
│   │   └── auth/              # Authentication pages
│   ├── components/            # React components
│   │   ├── ui/                # Reusable UI components
│   │   ├── sections/          # Page sections
│   │   └── shared/            # Shared components
│   ├── data/                  # Game data and configurations
│   ├── lib/                   # Utility functions and configurations
│   └── types/                 # TypeScript type definitions
├── prisma/                    # Database schema and migrations
├── public/                    # Static assets (images, icons, etc.)
├── messages/                  # Internationalization files
└── package.json              # Dependencies and scripts
```

## 🤝 Contributing

We welcome contributions to FreeHubGames! Here's how you can help:

### Development Workflow
1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Commit** your changes: `git commit -m 'Add amazing feature'`
4. **Push** to the branch: `git push origin feature/amazing-feature`
5. **Submit** a Pull Request

### Contribution Guidelines
- Follow the existing code style and conventions
- Add tests for new features when applicable
- Update documentation for any new functionality
- Ensure all tests pass before submitting PR
- Use meaningful commit messages

### Adding New Games
To add new games to the platform:
1. Update the game data in `src/data/games.ts`
2. Ensure proper categorization and internationalization
3. Add appropriate metadata and ratings
4. Test the game integration thoroughly

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Contact & Support

- **Author**: WenHaoFree
- **Email**: <EMAIL>
- **GitHub**: [@wenhaofree](https://github.com/wenhaofree)
- **Website**: [FreeHubGames.com](https://freehubgames.com)

### Community
- 🐛 **Bug Reports**: [GitHub Issues](https://github.com/wenhaofree/game-grove-web/issues)
- 💡 **Feature Requests**: [GitHub Discussions](https://github.com/wenhaofree/game-grove-web/discussions)
- 📧 **Business Inquiries**: <EMAIL>

---

<div align="center">

**⭐ Star this repository if you find it helpful!**

Made with ❤️ by [WenHaoFree](https://github.com/wenhaofree)

</div>
